#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频转字幕工具
使用OpenAI Whisper API实现音频文件转换为字幕
"""

# ==================== 配置项 ====================
# API配置
API_KEY = "sk-9iAGRW07EuhPheIiFU4lIGvkJwyM7Ph7k9Ldp4irTyNQK9Wg"
BASE_URL = "https://api.bianxieai.com"

# 音频配置
AUDIO_URL = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688b7ad899c624aa84b2560b.mp3"

# 模型配置
WHISPER_MODEL = "whisper-1"
TRANSLATION_MODEL = "gpt-3.5-turbo-0125"

# 翻译配置
USE_WHISPER_TRANSLATION = False  # 使用Whisper原生翻译（仅支持翻译为英文）
TARGET_LANGUAGE = "zh"  # 目标翻译语言：zh=中文, en=英文, ja=日语等
TRANSLATION_TEMPERATURE = 0.3  # 翻译温度，越低越稳定
MAX_TRANSLATION_TOKENS = 2000  # 翻译最大token数

# 输出配置
OUTPUT_FORMATS = ["srt", "json"]  # 输出格式：srt, txt, vtt, ass, json
# ================================================

import requests
import os
import tempfile
from typing import Optional, List, Dict
import json
import re


class AudioToSubtitle:
    """音频转字幕类"""

    def __init__(self, api_key: str = None, base_url: str = None):
        """
        初始化

        Args:
            api_key: OpenAI API密钥，默认使用配置项
            base_url: API基础URL，默认使用配置项
        """
        self.api_key = api_key or API_KEY
        self.base_url = (base_url or BASE_URL).rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def download_audio(self, audio_url: str) -> str:
        """
        下载音频文件到临时目录
        
        Args:
            audio_url: 音频文件URL
            
        Returns:
            临时文件路径
        """
        print(f"正在下载音频文件: {audio_url}")
        
        response = requests.get(audio_url, stream=True)
        response.raise_for_status()
        
        # 创建临时文件
        suffix = os.path.splitext(audio_url)[1] or '.mp3'
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
        
        # 下载文件
        for chunk in response.iter_content(chunk_size=8192):
            temp_file.write(chunk)
        
        temp_file.close()
        print(f"音频文件已下载到: {temp_file.name}")
        return temp_file.name
    
    def transcribe_audio(self, audio_file_path: str,
                        model: str = None,
                        language: Optional[str] = None,
                        response_format: str = "verbose_json") -> dict:
        """
        使用Whisper API转录音频

        Args:
            audio_file_path: 音频文件路径
            model: 使用的模型，默认whisper-1
            language: 音频语言，可选
            response_format: 响应格式，默认verbose_json（包含时间戳）

        Returns:
            转录结果
        """
        url = f"{self.base_url}/v1/audio/transcriptions"

        # 准备文件和数据
        with open(audio_file_path, 'rb') as audio_file:
            files = {
                'file': audio_file
            }

            data = {
                'model': model or WHISPER_MODEL,
                'response_format': response_format
            }

            if language:
                data['language'] = language

            print(f"正在调用Whisper API进行转录...")
            response = requests.post(url, headers=self.headers, files=files, data=data)

            if response.status_code != 200:
                print(f"API调用失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                response.raise_for_status()

            return response.json()

    def translate_audio(self, audio_file_path: str,
                       model: str = None,
                       response_format: str = "verbose_json") -> dict:
        """
        使用Whisper API直接翻译音频为英文

        Args:
            audio_file_path: 音频文件路径
            model: 使用的模型，默认使用配置项
            response_format: 响应格式，默认verbose_json（包含时间戳）

        Returns:
            翻译结果（英文）
        """
        url = f"{self.base_url}/v1/audio/translations"

        # 准备文件和数据
        with open(audio_file_path, 'rb') as audio_file:
            files = {
                'file': audio_file
            }

            data = {
                'model': model or WHISPER_MODEL,
                'response_format': response_format
            }

            print(f"正在调用Whisper API进行音频翻译...")
            response = requests.post(url, headers=self.headers, files=files, data=data)

            if response.status_code != 200:
                print(f"API调用失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                response.raise_for_status()

            return response.json()
    
    def save_subtitle(self, transcription: dict, output_path: str, format_type: str = "srt"):
        """
        保存字幕文件

        Args:
            transcription: 转录结果
            output_path: 输出文件路径
            format_type: 字幕格式，支持srt, txt, vtt, ass, json
        """
        format_type = format_type.lower()
        if format_type == "srt":
            self._save_as_srt(transcription, output_path)
        elif format_type == "vtt":
            self._save_as_vtt(transcription, output_path)
        elif format_type == "ass":
            self._save_as_ass(transcription, output_path)
        elif format_type == "json":
            self._save_as_json(transcription, output_path)
        else:
            self._save_as_txt(transcription, output_path)
    
    def _save_as_txt(self, transcription: dict, output_path: str):
        """保存为纯文本格式"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(transcription.get('text', ''))
        print(f"字幕已保存为TXT格式: {output_path}")
    
    def _save_as_srt(self, transcription: dict, output_path: str):
        """保存为SRT格式"""
        with open(output_path, 'w', encoding='utf-8') as f:
            # 检查是否有segments（时间戳信息）
            segments = transcription.get('segments', [])

            if segments:
                # 有时间戳信息，生成详细的SRT
                for i, segment in enumerate(segments, 1):
                    start_time = self._format_timestamp(segment.get('start', 0))
                    end_time = self._format_timestamp(segment.get('end', 0))
                    text = segment.get('text', '')

                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{text}\n\n")
            else:
                # 没有时间戳信息，使用整体文本
                text = transcription.get('text', '')
                f.write("1\n")
                f.write("00:00:00,000 --> 99:59:59,999\n")
                f.write(f"{text}\n\n")

        print(f"字幕已保存为SRT格式: {output_path}")

    def _format_timestamp(self, seconds: float, format_type: str = "srt") -> str:
        """将秒数转换为字幕时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)

        if format_type == "vtt":
            return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"
        elif format_type == "ass":
            centiseconds = int((seconds % 1) * 100)
            return f"{hours:01d}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"
        else:  # srt format
            return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _save_as_vtt(self, transcription: dict, output_path: str):
        """保存为WebVTT格式"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("WEBVTT\n\n")

            segments = transcription.get('segments', [])

            if segments:
                for segment in segments:
                    start_time = self._format_timestamp(segment.get('start', 0), "vtt")
                    end_time = self._format_timestamp(segment.get('end', 0), "vtt")
                    text = segment.get('text', '')

                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{text}\n\n")
            else:
                text = transcription.get('text', '')
                f.write("00:00:00.000 --> 99:59:59.999\n")
                f.write(f"{text}\n\n")

        print(f"字幕已保存为VTT格式: {output_path}")

    def _save_as_ass(self, transcription: dict, output_path: str):
        """保存为ASS格式"""
        with open(output_path, 'w', encoding='utf-8') as f:
            # ASS文件头
            f.write("[Script Info]\n")
            f.write("Title: Generated Subtitle\n")
            f.write("ScriptType: v4.00+\n\n")

            f.write("[V4+ Styles]\n")
            f.write("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n")
            f.write("Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1\n\n")

            f.write("[Events]\n")
            f.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")

            segments = transcription.get('segments', [])

            if segments:
                for segment in segments:
                    start_time = self._format_timestamp(segment.get('start', 0), "ass")
                    end_time = self._format_timestamp(segment.get('end', 0), "ass")
                    text = segment.get('text', '')

                    f.write(f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}\n")
            else:
                text = transcription.get('text', '')
                f.write(f"Dialogue: 0,0:00:00.00,9:59:59.99,Default,,0,0,0,,{text}\n")

        print(f"字幕已保存为ASS格式: {output_path}")

    def _save_as_json(self, transcription: dict, output_path: str):
        """保存为JSON格式（原始API返回内容）"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(transcription, f, ensure_ascii=False, indent=2)
        print(f"原始API返回内容已保存为JSON格式: {output_path}")

    def translate_text(self, text: str, target_language: str = None) -> str:
        """
        使用OpenAI API翻译文本

        Args:
            text: 要翻译的文本
            target_language: 目标语言代码，默认使用配置项

        Returns:
            翻译后的文本
        """
        target_language = target_language or TARGET_LANGUAGE
        url = f"{self.base_url}/v1/chat/completions"

        # 语言映射
        language_map = {
            "zh": "中文",
            "en": "English",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语",
            "ar": "阿拉伯语",
            "pt": "葡萄牙语",
            "it": "意大利语"
        }

        target_lang_name = language_map.get(target_language, target_language)

        prompt = f"请将以下文本翻译成{target_lang_name}，只返回翻译结果，不要添加任何解释：\n\n{text}"

        data = {
            "model": TRANSLATION_MODEL,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": TRANSLATION_TEMPERATURE,
            "max_tokens": MAX_TRANSLATION_TOKENS
        }

        try:
            response = requests.post(url, headers=self.headers, json=data)

            if response.status_code != 200:
                print(f"翻译API调用失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return text  # 返回原文本

            result = response.json()
            translated_text = result['choices'][0]['message']['content']
            return translated_text

        except Exception as e:
            print(f"翻译过程中出现错误: {e}")
            return text  # 返回原文本

    def translate_subtitle(self, input_path: str, output_path: str,
                          target_language: str = None, format_type: str = "srt"):
        """
        翻译字幕文件

        Args:
            input_path: 输入字幕文件路径
            output_path: 输出字幕文件路径
            target_language: 目标语言代码，默认使用配置项
            format_type: 输出格式
        """
        target_language = target_language or TARGET_LANGUAGE
        # 读取原字幕文件
        if input_path.endswith('.srt'):
            transcription = self._parse_srt_file(input_path)
        else:
            # 如果是txt文件，直接读取
            with open(input_path, 'r', encoding='utf-8') as f:
                text = f.read()
            transcription = {'text': text, 'segments': []}

        # 翻译文本
        print(f"正在翻译字幕到{target_language}...")

        if transcription.get('segments'):
            # 有时间戳信息，逐段翻译
            for segment in transcription['segments']:
                original_text = segment['text']
                translated_text = self.translate_text(original_text, target_language)
                segment['text'] = translated_text

            # 更新整体文本，保持原始格式
            transcription['text'] = ''.join([seg['text'] for seg in transcription['segments']])
        else:
            # 没有时间戳，翻译整体文本
            original_text = transcription['text']
            translated_text = self.translate_text(original_text, target_language)
            transcription['text'] = translated_text

        # 保存翻译后的字幕
        self.save_subtitle(transcription, output_path, format_type)
        print(f"翻译完成，已保存到: {output_path}")

    def _parse_srt_file(self, srt_path: str) -> Dict:
        """解析SRT文件"""
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        segments = []
        blocks = content.strip().split('\n\n')

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 解析时间戳
                time_line = lines[1]
                time_match = re.match(r'(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})', time_line)

                if time_match:
                    start_h, start_m, start_s, start_ms = map(int, time_match.groups()[:4])
                    end_h, end_m, end_s, end_ms = map(int, time_match.groups()[4:])

                    start_time = start_h * 3600 + start_m * 60 + start_s + start_ms / 1000
                    end_time = end_h * 3600 + end_m * 60 + end_s + end_ms / 1000

                    text = '\n'.join(lines[2:])

                    segments.append({
                        'start': start_time,
                        'end': end_time,
                        'text': text
                    })

        # 合并所有文本，保持原始格式
        full_text = ''.join([seg['text'] for seg in segments])

        return {
            'text': full_text,
            'segments': segments
        }
    
    def process_audio_url(self, audio_url: str, 
                         output_path: str = "subtitle.txt",
                         language: Optional[str] = None,
                         format_type: str = "txt") -> str:
        """
        处理音频URL，完整的转录流程
        
        Args:
            audio_url: 音频文件URL
            output_path: 输出文件路径
            language: 音频语言
            format_type: 输出格式
            
        Returns:
            转录的文本内容
        """
        temp_file = None
        try:
            # 下载音频文件
            temp_file = self.download_audio(audio_url)
            
            # 转录音频
            transcription = self.transcribe_audio(temp_file, language=language)
            
            # 保存字幕
            self.save_subtitle(transcription, output_path, format_type)
            
            # 返回转录文本
            return transcription.get('text', '')
            
        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")


def main():
    """主函数"""
    print("🎵 音频转字幕工具")
    print("=" * 30)
    print(f"音频地址: {AUDIO_URL}")
    print(f"目标语言: {TARGET_LANGUAGE}")
    print(f"输出格式: {OUTPUT_FORMATS}")
    print("=" * 30)

    # 创建转录器（使用配置项）
    transcriber = AudioToSubtitle()
    
    try:
        # 处理音频转录
        print("开始音频转字幕处理...")

        # 首先获取转录结果
        temp_file = transcriber.download_audio(AUDIO_URL)
        transcription = transcriber.transcribe_audio(temp_file)  # 自动检测语言

        # 生成多种格式的字幕文件
        print("\n生成多种格式字幕文件...")
        # SRT格式
        transcriber.save_subtitle(transcription, "subtitle.srt", "srt")
        # JSON格式（原始API返回内容）
        transcriber.save_subtitle(transcription, "subtitle.json", "json")


        # 清理临时文件
        import os
        if temp_file and os.path.exists(temp_file):
            os.unlink(temp_file)
            print(f"已清理临时文件: {temp_file}")

        print("\n转录完成！")
        print("转录内容:")
        print("-" * 50)
        print(transcription.get('text', ''))
        print("-" * 50)

        # 翻译功能
        if USE_WHISPER_TRANSLATION and TARGET_LANGUAGE == "en":
            print(f"\n使用Whisper原生翻译功能直接翻译为英文...")

            # 重新下载音频并使用Whisper翻译
            temp_file = transcriber.download_audio(AUDIO_URL)
            translation_result = transcriber.translate_audio(temp_file)

            # 保存翻译结果
            transcriber.save_subtitle(translation_result, "subtitle_en.srt", "srt")

            # 清理临时文件
            import os
            if temp_file and os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")

            print("\n生成的字幕文件:")
            print("- subtitle.srt (原文)")
            print("- subtitle.json (原始API返回内容)")
            print("- subtitle_en.srt (Whisper直接翻译)")

        else:
            print(f"\n使用GPT翻译字幕为{TARGET_LANGUAGE}...")

            # 使用GPT进行翻译
            output_file = f"subtitle_{TARGET_LANGUAGE}.srt"
            transcriber.translate_subtitle("subtitle.srt", output_file)

            print("\n生成的字幕文件:")
            print("- subtitle.srt (原文)")
            print("- subtitle.json (原始API返回内容)")
            print(f"- {output_file} (GPT翻译)")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
